import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { Button } from "@/shared/components/ui/button";
import { Download, X, FileText } from "lucide-react";
import { motion } from "framer-motion";
import { IDashboardReport } from "../../types/dashboard.types";
import React from "react";

interface IReportModalProps {
	reportId: number;
	reports: IDashboardReport[];
	onClose: () => void;
	onGeneratePDF: (reportId: number) => void;
}

export const ReportModal = React.memo(({ 
	reportId, 
	reports, 
	onClose, 
	onGeneratePDF 
}: IReportModalProps) => {
	const report = reports.find(r => r.id === reportId);

	if (!report) return null;

	const handleGeneratePDF = () => {
		onGeneratePDF(reportId);
	};

	return (
		<OverlayContainer isVisible={true} onClose={onClose}>
			<motion.div
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				exit={{ opacity: 0, scale: 0.95 }}
				transition={{ duration: 0.2 }}
				className="bg-white rounded-[20px] max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl"
				onClick={(e) => e.stopPropagation()}
			>
				{/* Header */}
				<div className="flex items-center justify-between p-6 border-b border-gray-200">
					<div className="flex items-center gap-3">
						<div className="p-2 bg-blue-100 rounded-lg">
							<FileText size={20} className="text-blue-600" />
						</div>
						<div>
							<h2 className="text-lg font-semibold text-gray-900">{report.nome}</h2>
							<p className="text-sm text-gray-500">{report.descricao}</p>
						</div>
					</div>
					
					<div className="flex items-center gap-2">
						<Button
							onClick={handleGeneratePDF}
							className="flex items-center gap-2 bg-mainColor hover:bg-mainColor/90 text-white"
						>
							<Download size={16} />
							Exportar PDF
						</Button>
						<Button
							variant="outline"
							onClick={onClose}
							className="p-2"
						>
							<X size={16} />
						</Button>
					</div>
				</div>

				{/* Content */}
				<div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
					<div className="space-y-6">
						{/* Report Description */}
						<div className="bg-gray-50 p-4 rounded-lg">
							<p className="text-sm text-gray-600">
								Conteúdo detalhado do relatório "{report.nome}". 
								Aqui seriam exibidos os dados completos do relatório com tabelas, 
								gráficos e análises detalhadas baseadas nos filtros selecionados.
							</p>
						</div>

						{/* Sample Chart Area */}
						<div className="border border-gray-200 rounded-lg p-6 bg-gray-50">
							<div className="flex items-center justify-center h-64 text-gray-500">
								<div className="text-center">
									<FileText size={48} className="mx-auto mb-4 text-gray-400" />
									<p className="text-lg font-medium">Visualização do Relatório</p>
									<p className="text-sm">
										Aqui seria exibido o conteúdo específico do relatório "{report.nome}"
									</p>
								</div>
							</div>
						</div>

						{/* Sample Data Table */}
						<div className="border border-gray-200 rounded-lg overflow-hidden">
							<div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
								<h3 className="font-medium text-gray-900">Dados do Relatório</h3>
							</div>
							<div className="overflow-x-auto">
								<table className="min-w-full divide-y divide-gray-200">
									<thead className="bg-gray-50">
										<tr>
											<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
												Item
											</th>
											<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
												Quantidade
											</th>
											<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
												Valor
											</th>
											<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
												Status
											</th>
										</tr>
									</thead>
									<tbody className="bg-white divide-y divide-gray-200">
										{[1, 2, 3, 4, 5].map(item => (
											<tr key={item} className="hover:bg-gray-50">
												<td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
													Item {item}
												</td>
												<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
													{Math.floor(Math.random() * 100)}
												</td>
												<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
													R$ {(Math.random() * 1000).toFixed(2)}
												</td>
												<td className="px-6 py-4 whitespace-nowrap">
													<span
														className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
															item % 3 === 0
																? "bg-red-100 text-red-800"
																: item % 2 === 0
																	? "bg-green-100 text-green-800"
																	: "bg-yellow-100 text-yellow-800"
														}`}
													>
														{item % 3 === 0 ? "Crítico" : item % 2 === 0 ? "Normal" : "Atenção"}
													</span>
												</td>
											</tr>
										))}
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</motion.div>
		</OverlayContainer>
	);
});

ReportModal.displayName = "ReportModal";
