import { MapP<PERSON>, TrendingUp } from "lucide-react";
import { CartesianGrid, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, <PERSON>Axi<PERSON> } from "recharts";
import { IChartData } from "../../types/dashboard.types";
import React from "react";

interface IClientsChartProps {
	data: IChartData[];
}

const CustomTooltip = ({ active, payload, label }: any) => {
	if (active && payload && payload.length) {
		return (
			<div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
				<p className="text-sm font-medium text-gray-900">{`${label}`}</p>
				<p className="text-sm text-green-600">
					{`Clientes: ${payload[0].value}`}
				</p>
			</div>
		);
	}
	return null;
};

export const ClientsChart = React.memo(({ data }: IClientsChartProps) => {
	return (
		<div className="bg-white p-6 rounded-[15px] shadow-sm border border-gray-100">
			<div className="flex justify-between items-center mb-6">
				<div className="flex items-center gap-3">
					<div className="p-2 bg-green-100 rounded-lg">
						<MapPin size={20} className="text-green-600" />
					</div>
					<div>
						<h3 className="font-semibold text-gray-900">Clientes por Região</h3>
						<p className="text-sm text-gray-500">Distribuição geográfica dos clientes</p>
					</div>
				</div>
				<button className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 transition-colors">
					<TrendingUp size={16} />
					Ver detalhes
				</button>
			</div>
			
			<div className="h-[300px] w-full">
				<ResponsiveContainer width="100%" height="100%">
					<LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
						<CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
						<XAxis 
							dataKey="name" 
							axisLine={false}
							tickLine={false}
							tick={{ fontSize: 12, fill: '#6b7280' }}
						/>
						<YAxis 
							axisLine={false}
							tickLine={false}
							tick={{ fontSize: 12, fill: '#6b7280' }}
						/>
						<Tooltip content={<CustomTooltip />} />
						<Line 
							type="monotone" 
							dataKey="valor" 
							stroke="#10b981" 
							strokeWidth={3}
							dot={{ fill: '#10b981', strokeWidth: 2, r: 6 }}
							activeDot={{ r: 8, stroke: '#10b981', strokeWidth: 2 }}
							className="drop-shadow-sm"
						/>
					</LineChart>
				</ResponsiveContainer>
			</div>
		</div>
	);
});

ClientsChart.displayName = "ClientsChart";
