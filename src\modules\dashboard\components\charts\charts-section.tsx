import { CardLMPContainer } from "@/shared/components/custom/card";
import { BarChart3 } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { IDashboardChartsData, PeriodType } from "../../types/dashboard.types";
import { SalesChart } from "./sales-chart";
import { ProductsChart } from "./products-chart";
import { ClientsChart } from "./clients-chart";
import React from "react";

interface IChartsSectionProps {
	selectedPeriod: PeriodType;
	onPeriodChange: (period: PeriodType) => void;
	chartsData: IDashboardChartsData;
}

const PeriodButton = React.memo(({ 
	period, 
	label, 
	isActive, 
	onClick 
}: { 
	period: PeriodType; 
	label: string; 
	isActive: boolean; 
	onClick: (period: PeriodType) => void; 
}) => (
	<button
		className={`
			px-4 py-2 font-medium text-sm rounded-lg transition-all duration-200
			${isActive 
				? "bg-mainColor text-white shadow-sm" 
				: "text-gray-500 hover:text-gray-700 hover:bg-gray-100"
			}
		`}
		onClick={() => onClick(period)}
	>
		{label}
	</button>
));

PeriodButton.displayName = "PeriodButton";

export const ChartsSection = React.memo(({ 
	selectedPeriod, 
	onPeriodChange, 
	chartsData 
}: IChartsSectionProps) => {
	const periods: { period: PeriodType; label: string }[] = [
		{ period: "diario", label: "Diário" },
		{ period: "semanal", label: "Semanal" },
		{ period: "mensal", label: "Mensal" },
		{ period: "anual", label: "Anual" },
	];

	return (
		<CardLMPContainer
			icon={<BarChart3 size={22} className="text-gray-500" />}
			title="Análise de Dados"
			description="Visualize os dados do seu negócio em diferentes perspectivas"
		>
			{/* Period Selector */}
			<div className="flex flex-wrap gap-2 mb-6 p-1 bg-gray-100 rounded-lg w-fit">
				{periods.map(({ period, label }) => (
					<PeriodButton
						key={period}
						period={period}
						label={label}
						isActive={selectedPeriod === period}
						onClick={onPeriodChange}
					/>
				))}
			</div>

			{/* Charts Grid */}
			<AnimatePresence mode="wait">
				<motion.div
					key={selectedPeriod}
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					exit={{ opacity: 0, y: -20 }}
					transition={{ duration: 0.3 }}
					className="grid grid-cols-1 lg:grid-cols-2 gap-6"
				>
					{/* Sales Chart */}
					<motion.div
						initial={{ opacity: 0, x: -20 }}
						animate={{ opacity: 1, x: 0 }}
						transition={{ duration: 0.3, delay: 0.1 }}
					>
						<SalesChart data={chartsData.vendasMensais} />
					</motion.div>

					{/* Products Chart */}
					<motion.div
						initial={{ opacity: 0, x: 20 }}
						animate={{ opacity: 1, x: 0 }}
						transition={{ duration: 0.3, delay: 0.2 }}
					>
						<ProductsChart data={chartsData.produtosMaisVendidos} />
					</motion.div>

					{/* Clients Chart - Full Width */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.3, delay: 0.3 }}
						className="lg:col-span-2"
					>
						<ClientsChart data={chartsData.clientesPorRegiao} />
					</motion.div>
				</motion.div>
			</AnimatePresence>
		</CardLMPContainer>
	);
});

ChartsSection.displayName = "ChartsSection";
