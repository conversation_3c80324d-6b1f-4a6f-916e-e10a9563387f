import { CardLMPContainer } from "@/shared/components/custom/card";
import { FileText, Download, Eye } from "lucide-react";
import { motion } from "framer-motion";
import { IDashboardReport } from "../../types/dashboard.types";
import { ReportModal } from "./report-modal";
import React from "react";

interface IReportsSectionProps {
	reports: IDashboardReport[];
	activeReport: number | null;
	onOpenReport: (reportId: number) => void;
	onCloseReport: () => void;
	onGeneratePDF: (reportId: number) => void;
}

const ReportCard = React.memo(({ 
	report, 
	onOpenReport, 
	onGeneratePDF 
}: { 
	report: IDashboardReport; 
	onOpenReport: (id: number) => void; 
	onGeneratePDF: (id: number) => void; 
}) => (
	<motion.div
		initial={{ opacity: 0, y: 10 }}
		animate={{ opacity: 1, y: 0 }}
		className="flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group"
	>
		<div className="flex items-center gap-3 flex-1">
			<div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow transition-shadow">
				<FileText size={18} className="text-gray-600" />
			</div>
			<div className="flex-1 min-w-0">
				<h4 className="font-medium text-gray-900 truncate">{report.nome}</h4>
				<p className="text-sm text-gray-500 truncate">{report.descricao}</p>
			</div>
		</div>
		
		<div className="flex items-center gap-2 ml-4">
			<button
				onClick={() => onOpenReport(report.id)}
				className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
				title="Visualizar relatório"
			>
				<Eye size={16} />
			</button>
			<button
				onClick={() => onGeneratePDF(report.id)}
				className="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
				title="Baixar PDF"
			>
				<Download size={16} />
			</button>
		</div>
	</motion.div>
));

ReportCard.displayName = "ReportCard";

export const ReportsSection = React.memo(({ 
	reports, 
	activeReport, 
	onOpenReport, 
	onCloseReport, 
	onGeneratePDF 
}: IReportsSectionProps) => {
	return (
		<>
			<CardLMPContainer
				icon={<FileText size={22} className="text-gray-500" />}
				title="Relatórios"
				description="Acesse e gere relatórios detalhados do seu negócio"
				actions={
					<button className="hidden md:flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 transition-colors">
						<FileText size={16} />
						Todos os relatórios
					</button>
				}
			>
				<div className="space-y-3">
					{reports.map((report, index) => (
						<motion.div
							key={report.id}
							initial={{ opacity: 0, x: -20 }}
							animate={{ opacity: 1, x: 0 }}
							transition={{ duration: 0.3, delay: index * 0.1 }}
						>
							<ReportCard
								report={report}
								onOpenReport={onOpenReport}
								onGeneratePDF={onGeneratePDF}
							/>
						</motion.div>
					))}
				</div>

				{/* Mobile Actions */}
				<div className="md:hidden mt-4 pt-4 border-t border-gray-200">
					<button className="w-full flex items-center justify-center gap-2 text-sm text-blue-600 hover:text-blue-700 transition-colors py-2">
						<FileText size={16} />
						Ver todos os relatórios
					</button>
				</div>
			</CardLMPContainer>

			{/* Report Modal */}
			{activeReport && (
				<ReportModal
					reportId={activeReport}
					reports={reports}
					onClose={onCloseReport}
					onGeneratePDF={onGeneratePDF}
				/>
			)}
		</>
	);
});

ReportsSection.displayName = "ReportsSection";
