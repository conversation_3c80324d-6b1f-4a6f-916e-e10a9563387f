import { Package, TrendingUp } from "lucide-react";
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend } from "recharts";
import { IChartData } from "../../types/dashboard.types";
import React from "react";

interface IProductsChartProps {
	data: IChartData[];
}

const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

const CustomTooltip = ({ active, payload }: any) => {
	if (active && payload && payload.length) {
		return (
			<div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
				<p className="text-sm font-medium text-gray-900">{payload[0].name}</p>
				<p className="text-sm text-blue-600">
					{`Vendas: ${payload[0].value} unidades`}
				</p>
			</div>
		);
	}
	return null;
};

const CustomLegend = ({ payload }: any) => {
	return (
		<div className="flex flex-wrap justify-center gap-4 mt-4">
			{payload?.map((entry: any, index: number) => (
				<div key={index} className="flex items-center gap-2">
					<div 
						className="w-3 h-3 rounded-full" 
						style={{ backgroundColor: entry.color }}
					/>
					<span className="text-xs text-gray-600">{entry.value}</span>
				</div>
			))}
		</div>
	);
};

export const ProductsChart = React.memo(({ data }: IProductsChartProps) => {
	return (
		<div className="bg-white p-6 rounded-[15px] shadow-sm border border-gray-100">
			<div className="flex justify-between items-center mb-6">
				<div className="flex items-center gap-3">
					<div className="p-2 bg-green-100 rounded-lg">
						<Package size={20} className="text-green-600" />
					</div>
					<div>
						<h3 className="font-semibold text-gray-900">Produtos Mais Vendidos</h3>
						<p className="text-sm text-gray-500">Distribuição de vendas por produto</p>
					</div>
				</div>
				<button className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 transition-colors">
					<TrendingUp size={16} />
					Ver detalhes
				</button>
			</div>
			
			<div className="h-[300px] w-full">
				<ResponsiveContainer width="100%" height="100%">
					<PieChart>
						<Pie
							data={data}
							cx="50%"
							cy="50%"
							outerRadius={80}
							dataKey="value"
							nameKey="name"
							label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
							labelLine={false}
						>
							{data.map((entry, index) => (
								<Cell 
									key={`cell-${index}`} 
									fill={COLORS[index % COLORS.length]}
									className="hover:opacity-80 transition-opacity cursor-pointer"
								/>
							))}
						</Pie>
						<Tooltip content={<CustomTooltip />} />
						<Legend content={<CustomLegend />} />
					</PieChart>
				</ResponsiveContainer>
			</div>
		</div>
	);
});

ProductsChart.displayName = "ProductsChart";
