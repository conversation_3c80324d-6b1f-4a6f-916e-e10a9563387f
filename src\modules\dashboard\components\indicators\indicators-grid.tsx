import { TrendingUp, DollarSign, Users, Package, Target, Sparkles, ArrowUpRight, ArrowDownRight } from "lucide-react";
import { motion } from "framer-motion";
import { IDashboardIndicator } from "../../types/dashboard.types";
import React from "react";

interface IIndicatorsGridProps {
	indicators: IDashboardIndicator[];
}

const getIndicatorConfig = (titulo: string) => {
	switch (titulo.toLowerCase()) {
		case "faturamento":
			return {
				icon: DollarSign,
				gradient: "from-emerald-500 to-teal-600",
				bgGradient: "from-emerald-50 to-teal-50",
				iconBg: "bg-gradient-to-br from-emerald-100 to-teal-100",
				iconColor: "text-emerald-600",
				accentColor: "emerald",
			};
		case "novos clientes":
			return {
				icon: Users,
				gradient: "from-blue-500 to-indigo-600",
				bgGradient: "from-blue-50 to-indigo-50",
				iconBg: "bg-gradient-to-br from-blue-100 to-indigo-100",
				iconColor: "text-blue-600",
				accentColor: "blue",
			};
		case "produtos vendidos":
			return {
				icon: Package,
				gradient: "from-purple-500 to-pink-600",
				bgGradient: "from-purple-50 to-pink-50",
				iconBg: "bg-gradient-to-br from-purple-100 to-pink-100",
				iconColor: "text-purple-600",
				accentColor: "purple",
			};
		case "tickets médio":
		case "ticket médio":
			return {
				icon: Target,
				gradient: "from-orange-500 to-red-600",
				bgGradient: "from-orange-50 to-red-50",
				iconBg: "bg-gradient-to-br from-orange-100 to-red-100",
				iconColor: "text-orange-600",
				accentColor: "orange",
			};
		default:
			return {
				icon: TrendingUp,
				gradient: "from-gray-500 to-slate-600",
				bgGradient: "from-gray-50 to-slate-50",
				iconBg: "bg-gradient-to-br from-gray-100 to-slate-100",
				iconColor: "text-gray-600",
				accentColor: "gray",
			};
	}
};

const IndicatorCard = React.memo(({ indicator }: { indicator: IDashboardIndicator }) => {
	const config = getIndicatorConfig(indicator.titulo);
	const IconComponent = config.icon;
	const isPositive = indicator.positivo;
	const TrendIcon = isPositive ? ArrowUpRight : ArrowDownRight;

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
			whileHover={{
				y: -4,
				transition: { duration: 0.2 },
			}}
			className={`
				relative overflow-hidden group cursor-pointer
				bg-gradient-to-br ${config.bgGradient}
				p-6 rounded-[20px] shadow-sm border border-white/50
				hover:shadow-xl hover:shadow-${config.accentColor}-500/10
				transition-all duration-300
				before:absolute before:inset-0 before:bg-gradient-to-br before:${config.gradient} before:opacity-0
				before:hover:opacity-5 before:transition-opacity before:duration-300
			`}
		>
			{/* Decorative elements */}
			<div className="absolute top-0 right-0 w-32 h-32 opacity-5">
				<div
					className={`w-full h-full bg-gradient-to-br ${config.gradient} rounded-full blur-3xl transform translate-x-16 -translate-y-16`}
				/>
			</div>

			{/* Sparkle effect */}
			<motion.div
				className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
				animate={{
					rotate: [0, 360],
					scale: [1, 1.1, 1],
				}}
				transition={{
					duration: 2,
					repeat: Infinity,
					ease: "linear",
				}}
			>
				<Sparkles size={16} className={`${config.iconColor} opacity-60`} />
			</motion.div>

			<div className="relative z-10">
				{/* Header */}
				<div className="flex items-center justify-between mb-4">
					<div className="flex items-center gap-3">
						<motion.div
							className={`p-3 rounded-xl ${config.iconBg} shadow-sm`}
							whileHover={{ scale: 1.05, rotate: 5 }}
							transition={{ duration: 0.2 }}
						>
							<IconComponent size={24} className={config.iconColor} />
						</motion.div>
						<div>
							<h3 className="text-sm font-semibold text-gray-700 mb-1">{indicator.titulo}</h3>
							<div className="flex items-center gap-1">
								<div className={`w-2 h-2 rounded-full bg-gradient-to-r ${config.gradient}`} />
								<span className="text-xs text-gray-500">Indicador</span>
							</div>
						</div>
					</div>
				</div>

				{/* Value */}
				<div className="space-y-3">
					<motion.p
						className="text-3xl font-bold text-gray-900 tracking-tight"
						initial={{ scale: 0.9 }}
						animate={{ scale: 1 }}
						transition={{ duration: 0.3, delay: 0.1 }}
					>
						{indicator.valor}
					</motion.p>

					{/* Trend */}
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<motion.div
								className={`
									flex items-center gap-1 px-2 py-1 rounded-lg
									${isPositive ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"}
								`}
								whileHover={{ scale: 1.05 }}
								transition={{ duration: 0.2 }}
							>
								<TrendIcon size={14} />
								<span className="text-sm font-semibold">{indicator.percentual}</span>
							</motion.div>
							<span className="text-xs text-gray-500">vs período anterior</span>
						</div>

						{/* Progress indicator */}
						<div className="flex items-center gap-1">
							{[...Array(3)].map((_, i) => (
								<motion.div
									key={i}
									className={`w-1 h-6 rounded-full ${isPositive ? "bg-green-200" : "bg-red-200"}`}
									initial={{ scaleY: 0.3 }}
									animate={{
										scaleY: isPositive ? [0.3, 1, 0.7][i] : [0.7, 0.4, 0.2][i],
										backgroundColor: isPositive ? ["#bbf7d0", "#16a34a", "#22c55e"][i] : ["#fecaca", "#dc2626", "#ef4444"][i],
									}}
									transition={{
										duration: 0.5,
										delay: i * 0.1,
										repeat: Infinity,
										repeatType: "reverse",
										repeatDelay: 2,
									}}
								/>
							))}
						</div>
					</div>
				</div>
			</div>
		</motion.div>
	);
});

IndicatorCard.displayName = "IndicatorCard";

export const IndicatorsGrid = React.memo(({ indicators }: IIndicatorsGridProps) => {
	return (
		<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
			{indicators.map((indicator, index) => (
				<motion.div
					key={indicator.id}
					initial={{ opacity: 0, y: 30, scale: 0.9 }}
					animate={{ opacity: 1, y: 0, scale: 1 }}
					transition={{
						duration: 0.4,
						delay: index * 0.1,
						type: "spring",
						stiffness: 100,
						damping: 15,
					}}
					whileInView={{
						opacity: 1,
						y: 0,
						transition: { duration: 0.6, delay: index * 0.1 },
					}}
					viewport={{ once: true, margin: "-50px" }}
				>
					<IndicatorCard indicator={indicator} />
				</motion.div>
			))}
		</div>
	);
});

IndicatorsGrid.displayName = "IndicatorsGrid";
