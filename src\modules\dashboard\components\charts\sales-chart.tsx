import { Bar<PERSON><PERSON>2, <PERSON><PERSON><PERSON>Up } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, <PERSON>A<PERSON><PERSON> } from "recharts";
import { IChartData } from "../../types/dashboard.types";
import React from "react";

interface ISalesChartProps {
	data: IChartData[];
}

const CustomTooltip = ({ active, payload, label }: any) => {
	if (active && payload && payload.length) {
		return (
			<div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
				<p className="text-sm font-medium text-gray-900">{`${label}`}</p>
				<p className="text-sm text-blue-600">
					{`Vendas: R$ ${payload[0].value?.toLocaleString('pt-BR')}`}
				</p>
			</div>
		);
	}
	return null;
};

export const SalesChart = React.memo(({ data }: ISalesChartProps) => {
	return (
		<div className="bg-white p-6 rounded-[15px] shadow-sm border border-gray-100">
			<div className="flex justify-between items-center mb-6">
				<div className="flex items-center gap-3">
					<div className="p-2 bg-blue-100 rounded-lg">
						<BarChart2 size={20} className="text-blue-600" />
					</div>
					<div>
						<h3 className="font-semibold text-gray-900">Vendas Mensais</h3>
						<p className="text-sm text-gray-500">Evolução das vendas ao longo do tempo</p>
					</div>
				</div>
				<button className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 transition-colors">
					<TrendingUp size={16} />
					Ver detalhes
				</button>
			</div>
			
			<div className="h-[300px] w-full">
				<ResponsiveContainer width="100%" height="100%">
					<BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
						<CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
						<XAxis 
							dataKey="name" 
							axisLine={false}
							tickLine={false}
							tick={{ fontSize: 12, fill: '#6b7280' }}
						/>
						<YAxis 
							axisLine={false}
							tickLine={false}
							tick={{ fontSize: 12, fill: '#6b7280' }}
							tickFormatter={(value) => `R$ ${value.toLocaleString('pt-BR')}`}
						/>
						<Tooltip content={<CustomTooltip />} />
						<Bar 
							dataKey="valor" 
							fill="#3b82f6" 
							radius={[4, 4, 0, 0]}
							className="hover:opacity-80 transition-opacity"
						/>
					</BarChart>
				</ResponsiveContainer>
			</div>
		</div>
	);
});

SalesChart.displayName = "SalesChart";
